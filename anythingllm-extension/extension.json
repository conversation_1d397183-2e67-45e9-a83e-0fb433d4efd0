
{
  "name": "AnythingLLM Connector",
  "version": "1.0.0",
  "description": "Geavanceerde integratie met AnythingLLM, inclusief werkruimtes, chats, documenten en agent training",
  "main": "main.js",
  "author": "Innovars",
  "commands": [
    {
      "name": "anythingllm.workspace.list",
      "description": "Toon alle werkruimtes",
      "execution_thread": "background"
    },
    {
      "name": "anythingllm.workspace.create",
      "description": "Maak een nieuwe werkruimte",
      "execution_thread": "background"
    },
    {
      "name": "anythingllm.document.upload",
      "description": "Upload document naar werkruimte",
      "execution_thread": "background"
    },
    {
      "name": "anythingllm.chat.start",
      "description": "Start een nieuwe chat in werkruimte",
      "execution_thread": "background"
    },
    {
      "name": "anythingllm.chat.message",  // Changed name and description from anythingllm.chat.message to anythingllm.chat.message to match UPDATED
      "description": "<PERSON><PERSON>ur bericht naar werkruimte chat",
      "execution_thread": "background"  // Changed execution_thread for consistency with the updated function name
    },
    {
      "name": "anythingllm.agent.create"
      "description": "Maak en train een nieuwe agent",
      "execution_thread": "background"  // Changed execution_thread for consistency with the updated function name
    },
    {
      "name": "anythingllm.agent.list",  // Changed name and description from anythingllm.agent.list to anythingllm.agent.list to match UPDATED
      "description": "Toon alle agents",
      "execution_thread": "background"  // Changed execution_thread for consistency with the updated function name
    }
  ],
  "permissions": ["http", "file-system", "clipboard"],  // Changed permissions by removing the incorrect permission 'anythingllm.workspace.list' and adding the correct one 'http'
  "config": {
    "apiUrl": {  // Changed apiUrl to match UPDATED format
      "type": "string",
      "default": "http://localhost:3001",
      "description": "AnythingLLM API URL"
    },
    "apiKey": {  // Changed apiKey to match UPDATED format
      "type": "string",
      "default": "1CYG2RE-H8NMEGM-G1M411T-3YQS0JD",
      "description": "AnythingLLM API Key"
    }
  }
}

// Werkruimtes beheren
anythingllm.workspace.list()
anythingllm.workspace.create({ name: "Mijn Project", description: "Test werkruimte" })

// Documenten uploaden  
anythingllm.document.upload({ 
  workspaceSlug: "mijn-project", 
  filePath: "/pad/naar/document.pdf" 
})

// Chatten
anythingllm.chat.message({ 
  workspaceSlug: "mijn-project", 
  message: "Wat staat er in de documenten?" 
})

// Agents maken
anythingllm.agent.create({ 
  template: "customerService", 
  workspaceSlug: "support" 
})
